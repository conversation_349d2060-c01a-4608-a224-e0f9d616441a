import turtle
import random
import time

# Setup screen
screen = turtle.Screen()
screen.title("Te<PERSON><PERSON>")
screen.bgcolor("black")
screen.setup(width=600, height=800)
screen.tracer(0)

# Constants
GRID_WIDTH = 10
GRID_HEIGHT = 20
BLOCK_SIZE = 30
GRID_LEFT = -(GRID_WIDTH * BLOCK_SIZE) // 2
GRID_BOTTOM = -(GRID_HEIGHT * BLOCK_SIZE) // 2

# Tetromino shapes
SHAPES = [
    [[1, 1, 1, 1]],  # I
    [[1, 1], [1, 1]],  # O
    [[1, 1, 1], [0, 1, 0]],  # T
    [[1, 1, 1], [1, 0, 0]],  # J
    [[1, 1, 1], [0, 0, 1]],  # L
    [[0, 1, 1], [1, 1, 0]],  # S
    [[1, 1, 0], [0, 1, 1]]   # Z
]

COLORS = ["cyan", "yellow", "purple", "blue", "orange", "green", "red"]

# Game grid (0 = empty, 1-7 = block colors)
grid = [[0 for _ in range(GRID_WIDTH)] for _ in range(GRID_HEIGHT)]

# Create turtle for drawing blocks
drawer = turtle.Turtle()
drawer.penup()
drawer.hideturtle()
drawer.speed(0)

# Current tetromino state
current_shape = None
current_color = None
current_x = 0
current_y = 0
current_rotation = 0

def draw_block(x, y, color):
    """Draw a single block at grid position (x, y)"""
    screen_x = GRID_LEFT + x * BLOCK_SIZE
    screen_y = GRID_BOTTOM + y * BLOCK_SIZE
    
    drawer.setposition(screen_x, screen_y)
    drawer.fillcolor(color)
    drawer.pencolor("white")
    drawer.pendown()
    drawer.begin_fill()
    
    # Draw a perfect square by using exact coordinates
    drawer.setheading(0)  # Reset heading to face east
    for _ in range(4):
        drawer.forward(BLOCK_SIZE)
        drawer.left(90)
        
    drawer.end_fill()
    drawer.penup()

def draw_grid():
    """Draw the entire grid"""
    drawer.clear()
    
    # Draw fixed blocks
    for y in range(GRID_HEIGHT):
        for x in range(GRID_WIDTH):
            if grid[y][x] > 0:
                draw_block(x, y, COLORS[grid[y][x]-1])
    
    # Draw current tetromino
    if current_shape:
        for y in range(len(current_shape)):
            for x in range(len(current_shape[y])):
                if current_shape[y][x] == 1:
                    draw_block(current_x + x, current_y + y, current_color)
    
    # Draw border
    drawer.pencolor("white")
    drawer.setposition(GRID_LEFT, GRID_BOTTOM)
    drawer.pendown()
    drawer.forward(GRID_WIDTH * BLOCK_SIZE)
    drawer.left(90)
    drawer.forward(GRID_HEIGHT * BLOCK_SIZE)
    drawer.left(90)
    drawer.forward(GRID_WIDTH * BLOCK_SIZE)
    drawer.left(90)
    drawer.forward(GRID_HEIGHT * BLOCK_SIZE)
    drawer.penup()
    
    screen.update()

def rotate_shape(shape):
    """Rotate the shape 90 degrees clockwise"""
    return [list(row) for row in zip(*shape[::-1])]

def new_tetromino():
    """Create a new random tetromino at the top of the grid"""
    global current_shape, current_color, current_x, current_y, current_rotation
    
    shape_idx = random.randint(0, len(SHAPES) - 1)
    current_shape = SHAPES[shape_idx]
    current_color = COLORS[shape_idx]
    current_x = GRID_WIDTH // 2 - len(current_shape[0]) // 2
    current_y = GRID_HEIGHT - len(current_shape)
    current_rotation = 0

def check_collision():
    """Check if current tetromino collides with walls or other blocks"""
    for y in range(len(current_shape)):
        for x in range(len(current_shape[y])):
            if current_shape[y][x] == 1:
                # Check if out of bounds
                if (current_x + x < 0 or current_x + x >= GRID_WIDTH or 
                    current_y + y < 0 or current_y + y >= GRID_HEIGHT):
                    return True
                # Check if collides with existing blocks
                if current_y + y >= 0 and grid[current_y + y][current_x + x] > 0:
                    return True
    return False

def merge_tetromino():
    """Merge the current tetromino into the grid"""
    for y in range(len(current_shape)):
        for x in range(len(current_shape[y])):
            if current_shape[y][x] == 1 and current_y + y >= 0:
                grid[current_y + y][current_x + x] = COLORS.index(current_color) + 1

def check_lines():
    """Check for completed lines and remove them"""
    lines_cleared = 0
    y = 0
    while y < GRID_HEIGHT:
        if all(grid[y]):
            # Remove the line
            grid.pop(y)
            # Add a new empty line at the top
            grid.append([0 for _ in range(GRID_WIDTH)])
            lines_cleared += 1
        else:
            y += 1
    return lines_cleared

def move_left():
    global current_x
    current_x -= 1
    if check_collision():
        current_x += 1
    draw_grid()

def move_right():
    global current_x
    current_x += 1
    if check_collision():
        current_x -= 1
    draw_grid()

def rotate():
    global current_shape, current_rotation
    old_shape = current_shape
    current_shape = rotate_shape(current_shape)
    current_rotation = (current_rotation + 1) % 4
    
    if check_collision():
        current_shape = old_shape
        current_rotation = (current_rotation - 1) % 4
    draw_grid()

def move_down():
    global current_y
    current_y -= 1
    if check_collision():
        current_y += 1
        merge_tetromino()
        check_lines()
        new_tetromino()
        if check_collision():
            return False  # Game over
    draw_grid()
    return True

def drop():
    """Drop the tetromino to the bottom"""
    while move_down():
        pass

# Set up keyboard controls
screen.listen()
screen.onkeypress(move_left, "Left")
screen.onkeypress(move_right, "Right")
screen.onkeypress(rotate, "Up")
screen.onkeypress(move_down, "Down")
screen.onkeypress(drop, "space")

# Start the game
new_tetromino()
draw_grid()

# Game loop
game_over = False
while not game_over:
    if not move_down():
        game_over = True
    time.sleep(0.5)  # Adjust speed here

# Game over message
drawer.goto(0, 0)
drawer.color("white")
drawer.write("GAME OVER", align="center", font=("Arial", 24, "normal"))
screen.update()

# Keep the window open
screen.mainloop()
