import turtle
import time

# Set up the screen
win = turtle.Screen()
win.title("Pong - Player vs Computer")
win.bgcolor("black")
win.setup(width=800, height=600)
win.tracer(0)

# Paddle class
class Paddle(turtle.Turtle):
    def __init__(self, x):
        super().__init__()
        self.speed(0)
        self.shape("square")
        self.color("white")
        self.shapesize(stretch_wid=6, stretch_len=1)
        self.penup()
        self.goto(x, 0)

    def move_up(self):
        if self.ycor() < 250:
            self.sety(self.ycor() + 20)

    def move_down(self):
        if self.ycor() > -250:
            self.sety(self.ycor() - 20)

# Ball
ball = turtle.Turtle()
ball.speed(0)
ball.shape("circle")
ball.color("white")
ball.penup()
ball.goto(0, 0)
ball.dx = 0.7  # Faster ball speed
ball.dy = 0.7

# Paddles
player = Paddle(-350)
computer = Paddle(350)

# Keyboard bindings
win.listen()
win.onkeypress(player.move_up, "w")
win.onkeypress(player.move_down, "s")

# Score
player_score = 0
computer_score = 0

# Score display
pen = turtle.Turtle()
pen.speed(0)
pen.color("white")
pen.penup()
pen.hideturtle()
pen.goto(0, 260)
pen.write("Player: 0  Computer: 0", align="center", font=("Courier", 24, "normal"))

# Main game loop
while True:
    win.update()

    # Move ball
    ball.setx(ball.xcor() + ball.dx)
    ball.sety(ball.ycor() + ball.dy)

    # Border collision (top/bottom)
    if ball.ycor() > 290 or ball.ycor() < -290:
        ball.sety(ball.ycor())
        ball.dy *= -1

    # Ball out of bounds
    if ball.xcor() > 390:
        ball.goto(0, 0)
        ball.dx = -0.7
        ball.dy = 0.7
        player_score += 1
        pen.clear()
        pen.write(f"Player: {player_score}  Computer: {computer_score}", align="center", font=("Courier", 24, "normal"))

    if ball.xcor() < -390:
        ball.goto(0, 0)
        ball.dx = 0.7
        ball.dy = 0.7
        computer_score += 1
        pen.clear()
        pen.write(f"Player: {player_score}  Computer: {computer_score}", align="center", font=("Courier", 24, "normal"))

    # Paddle collisions
    if (340 < ball.xcor() < 350) and (computer.ycor() - 50 < ball.ycor() < computer.ycor() + 50):
        ball.setx(340)
        ball.dx *= -1.1  # Speed increases after bounce
        ball.dy *= 1.05

    if (-350 < ball.xcor() < -340) and (player.ycor() - 50 < ball.ycor() < player.ycor() + 50):
        ball.setx(-340)
        ball.dx *= -1.1
        ball.dy *= 1.05

    # Simple AI for computer paddle
    if computer.ycor() < ball.ycor() and abs(computer.ycor() - ball.ycor()) > 10:
        computer.move_up()
    elif computer.ycor() > ball.ycor() and abs(computer.ycor() - ball.ycor()) > 10:
        computer.move_down()

    time.sleep(0.005)  # Faster update speed
