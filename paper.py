import pygame
import random
import sys

# Initialize pygame
pygame.init()

# Screen dimensions
WIDTH, HEIGHT = 800, 600
SCREEN = pygame.display.set_mode((WIDTH, HEIGHT))
pygame.display.set_caption("Toilet Paper Panic")

# Colors
WHITE = (255, 255, 255)
BLACK = (0, 0, 0)
BLUE = (50, 50, 255)
GREEN = (0, 200, 0)
RED = (255, 0, 0)

# Game settings
FPS = 60
PLAYER_SPEED = 5
AI_SPEED = 3
SPAWN_INTERVAL = 2000  # milliseconds

# Load assets (use rectangles if no images)
player_img = pygame.Surface((40, 40))
player_img.fill(BLUE)

ai_img = pygame.Surface((40, 40))
ai_img.fill(RED)

tp_img = pygame.Surface((20, 20))
tp_img.fill(WHITE)

# Fonts
font = pygame.font.SysFont("Arial", 24)

# Game objects
class Player(pygame.sprite.Sprite):
    def __init__(self):
        super().__init__()
        self.image = player_img
        self.rect = self.image.get_rect(center=(WIDTH // 2, HEIGHT // 2))
        self.score = 0

    def update(self, keys):
        if keys[pygame.K_LEFT]:
            self.rect.x -= PLAYER_SPEED
        if keys[pygame.K_RIGHT]:
            self.rect.x += PLAYER_SPEED
        if keys[pygame.K_UP]:
            self.rect.y -= PLAYER_SPEED
        if keys[pygame.K_DOWN]:
            self.rect.y += PLAYER_SPEED

        self.rect.clamp_ip(SCREEN.get_rect())


class AIShopper(pygame.sprite.Sprite):
    def __init__(self):
        super().__init__()
        self.image = ai_img
        self.rect = self.image.get_rect()
        self.rect.x = random.randint(0, WIDTH)
        self.rect.y = random.randint(0, HEIGHT)

    def update(self, target):
        if self.rect.x < target.rect.x:
            self.rect.x += AI_SPEED
        elif self.rect.x > target.rect.x:
            self.rect.x -= AI_SPEED
        if self.rect.y < target.rect.y:
            self.rect.y += AI_SPEED
        elif self.rect.y > target.rect.y:
            self.rect.y -= AI_SPEED


class ToiletPaper(pygame.sprite.Sprite):
    def __init__(self):
        super().__init__()
        self.image = tp_img
        self.rect = self.image.get_rect()
        self.rect.x = random.randint(50, WIDTH - 50)
        self.rect.y = random.randint(50, HEIGHT - 50)


def draw_text(text, x, y):
    label = font.render(text, True, BLACK)
    SCREEN.blit(label, (x, y))


# Game loop
player = Player()
ai_shopper = AIShopper()
all_sprites = pygame.sprite.Group(player, ai_shopper)
tp_group = pygame.sprite.Group()

clock = pygame.time.Clock()
TP_EVENT = pygame.USEREVENT + 1
pygame.time.set_timer(TP_EVENT, SPAWN_INTERVAL)

running = True
while running:
    clock.tick(FPS)
    SCREEN.fill(GREEN)

    for event in pygame.event.get():
        if event.type == pygame.QUIT:
            running = False
        elif event.type == TP_EVENT:
            tp = ToiletPaper()
            tp_group.add(tp)
            all_sprites.add(tp)

    keys = pygame.key.get_pressed()
    player.update(keys)
    if tp_group:
        nearest_tp = min(tp_group, key=lambda tp: player.rect.distance_to(tp.rect))
        ai_shopper.update(nearest_tp)

    for tp in tp_group:
        if player.rect.colliderect(tp.rect):
            player.score += 1
            tp.kill()
        elif ai_shopper.rect.colliderect(tp.rect):
            tp.kill()

    all_sprites.draw(SCREEN)
    draw_text(f"Score: {player.score}", 10, 10)

    pygame.display.flip()

pygame.quit()
sys.exit()
