import pygame
import random
import sys

# Initialize pygame
pygame.init()

# Screen dimensions
WIDTH, HEIGHT = 800, 600
SCREEN = pygame.display.set_mode((WIDTH, HEIGHT))
pygame.display.set_caption("Toilet Paper Panic - Maze Edition")

# Colors
WHITE = (255, 255, 255)
BLACK = (0, 0, 0)
BLUE = (50, 50, 255)
GREEN = (0, 200, 0)
RED = (255, 0, 0)
BROWN = (139, 69, 19)  # Wall color
GRAY = (128, 128, 128)  # Alternative wall color

# Game settings
FPS = 60
PLAYER_SPEED = 5
AI_SPEED = 5
SPAWN_INTERVAL = 2000  # milliseconds
WALL_SIZE = 40  # Size of wall blocks

# Load assets (use rectangles if no images)
player_img = pygame.Surface((40, 40))
player_img.fill(BLUE)

ai_img = pygame.Surface((40, 40))
ai_img.fill(RED)

tp_img = pygame.Surface((20, 20))
tp_img.fill(WHITE)

wall_img = pygame.Surface((WALL_SIZE, WALL_SIZE))
wall_img.fill(BROWN)

# Fonts
font = pygame.font.SysFont("Arial", 24)

# Maze layout (1 = wall, 0 = empty space)
# 20 columns x 15 rows (40px each = 800x600)
MAZE = [
    [1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1],
    [1,0,0,0,0,1,0,0,0,0,0,0,0,0,1,0,0,0,0,1],
    [1,0,1,1,0,1,0,1,1,1,1,1,1,0,1,0,1,1,0,1],
    [1,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,1],
    [1,1,1,0,1,1,1,0,1,1,1,1,0,1,1,1,0,1,1,1],
    [1,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,1],
    [1,0,1,1,1,0,1,1,0,1,1,0,1,1,0,1,1,1,0,1],
    [1,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,1],
    [1,0,1,1,1,0,1,1,0,1,1,0,1,1,0,1,1,1,0,1],
    [1,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,1],
    [1,1,1,0,1,1,1,0,1,1,1,1,0,1,1,1,0,1,1,1],
    [1,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,1],
    [1,0,1,1,0,1,0,1,1,1,1,1,1,0,1,0,1,1,0,1],
    [1,0,0,0,0,1,0,0,0,0,0,0,0,0,1,0,0,0,0,1],
    [1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1]
]

# Create wall sprites
class Wall(pygame.sprite.Sprite):
    def __init__(self, x, y):
        super().__init__()
        self.image = wall_img
        self.rect = pygame.Rect(x, y, WALL_SIZE, WALL_SIZE)

# Game objects
class Player(pygame.sprite.Sprite):
    def __init__(self, walls):
        super().__init__()
        self.image = player_img
        # Start in a guaranteed open space (row 1, col 1 in maze = position 40,40)
        self.rect = self.image.get_rect()
        self.rect.x = 15  # Column 1 * 40
        self.rect.y = 40  # Row 1 * 40
        self.score = 0
        self.walls = walls

    def update(self, keys):
        old_x, old_y = self.rect.x, self.rect.y

        if keys[pygame.K_LEFT]:
            self.rect.x -= PLAYER_SPEED
        if keys[pygame.K_RIGHT]:
            self.rect.x += PLAYER_SPEED
        if keys[pygame.K_UP]:
            self.rect.y -= PLAYER_SPEED
        if keys[pygame.K_DOWN]:
            self.rect.y += PLAYER_SPEED

        # Check for wall collisions
        if pygame.sprite.spritecollide(self, self.walls, False):
            self.rect.x, self.rect.y = old_x, old_y

        self.rect.clamp_ip(SCREEN.get_rect())


class AIShopper(pygame.sprite.Sprite):
    def __init__(self, walls):
        super().__init__()
        self.image = ai_img
        self.rect = self.image.get_rect()
        # Start AI in a guaranteed open space (row 13, col 18 in maze)
        self.rect.x = 18 * 40  # Column 18 * 40 = 720
        self.rect.y = 13 * 40  # Row 13 * 40 = 520
        self.walls = walls

    def update(self, target):
        if target is None:
            return

        old_x, old_y = self.rect.x, self.rect.y

        # Simple AI movement toward target
        if self.rect.x < target.rect.x:
            self.rect.x += AI_SPEED
        elif self.rect.x > target.rect.x:
            self.rect.x -= AI_SPEED

        # Check for wall collision on X movement
        if pygame.sprite.spritecollide(self, self.walls, False):
            self.rect.x = old_x

        old_x, old_y = self.rect.x, self.rect.y

        if self.rect.y < target.rect.y:
            self.rect.y += AI_SPEED
        elif self.rect.y > target.rect.y:
            self.rect.y -= AI_SPEED

        # Check for wall collision on Y movement
        if pygame.sprite.spritecollide(self, self.walls, False):
            self.rect.y = old_y


class ToiletPaper(pygame.sprite.Sprite):
    def __init__(self, walls):
        super().__init__()
        self.image = tp_img
        self.rect = self.image.get_rect()
        self.walls = walls
        self.spawn_in_open_space()

    def spawn_in_open_space(self):
        # Keep trying random positions until we find one not in a wall
        attempts = 0
        while attempts < 100:  # Prevent infinite loop
            self.rect.x = random.randint(50, WIDTH - 50)
            self.rect.y = random.randint(50, HEIGHT - 50)
            if not pygame.sprite.spritecollide(self, self.walls, False):
                break
            attempts += 1


def draw_text(text, x, y):
    label = font.render(text, True, BLACK)
    SCREEN.blit(label, (x, y))



def get_distance(rect1, rect2):
    dx = rect1.centerx - rect2.centerx
    dy = rect1.centery - rect2.centery
    return (dx ** 2 + dy ** 2) ** 0.5

# Create maze walls
walls = pygame.sprite.Group()
for row in range(len(MAZE)):
    for col in range(len(MAZE[row])):
        if MAZE[row][col] == 1:
            wall = Wall(col * WALL_SIZE, row * WALL_SIZE)
            walls.add(wall)

# Game loop
player = Player(walls)
ai_shopper = AIShopper(walls)
all_sprites = pygame.sprite.Group(player, ai_shopper)
all_sprites.add(walls)  # Add walls to all_sprites for drawing
tp_group = pygame.sprite.Group()

clock = pygame.time.Clock()
TP_EVENT = pygame.USEREVENT + 1
pygame.time.set_timer(TP_EVENT, SPAWN_INTERVAL)

running = True
while running:
    clock.tick(FPS)
    SCREEN.fill(BLACK)

    for event in pygame.event.get():
        if event.type == pygame.QUIT:
            running = False
        elif event.type == TP_EVENT:
            tp = ToiletPaper(walls)
            tp_group.add(tp)
            all_sprites.add(tp)

    keys = pygame.key.get_pressed()
    player.update(keys)
    if tp_group:
        nearest_tp = min(tp_group, key=lambda tp: get_distance(ai_shopper.rect, tp.rect))

        ai_shopper.update(nearest_tp)

    for tp in tp_group:
        if player.rect.colliderect(tp.rect):
            player.score += 1
            tp.kill()
        elif ai_shopper.rect.colliderect(tp.rect):
            tp.kill()

    all_sprites.draw(SCREEN)
    draw_text(f"Score: {player.score}", 10, 10)

    pygame.display.flip()

pygame.quit()
sys.exit()
