import tkinter as tk
from tkinter import messagebox
import random

# List of questions: each is (question, [option1, option2, option3], correct_option_index)
questions = [
    ("What is the capital of France?", ["Paris", "London", "Berlin"], 0),
    ("Which planet is known as the Red Planet?", ["Earth", "Mars", "Jupiter"], 1),
    ("Who wrote 'Hamlet'?", ["Charles Dickens", "William Shakespeare", "Mark Twain"], 1),
    ("What is the chemical symbol for water?", ["H2O", "O2", "CO2"], 0),
    ("What is the largest mammal?", ["Elephant", "Blue Whale", "Giraffe"], 1),
    ("In which continent is Egypt?", ["Asia", "Europe", "Africa"], 2),
    ("Which language is primarily spoken in Brazil?", ["Spanish", "Portuguese", "French"], 1),
    ("What color do you get by mixing red and white?", ["Pink", "Purple", "Orange"], 0),
    ("How many continents are there?", ["5", "6", "7"], 2),
    ("What gas do plants absorb from the air?", ["Oxygen", "Carbon Dioxide", "Nitrogen"], 1),
    ("Who painted the Mona Lisa?", ["Van Gogh", "Leonardo da Vinci", "Pablo Picasso"], 1),
    ("What is the boiling point of water in Celsius?", ["100", "90", "80"], 0),
    ("Which ocean is the largest?", ["Atlantic", "Pacific", "Indian"], 1),
]

class QuizGame:
    def __init__(self, master):
        self.master = master
        self.master.title("Common Knowledge Quiz")
        self.score = 0
        self.question_number = 0

        self.questions = random.sample(questions, 10)  # Pick 10 random questions

        self.question_label = tk.Label(master, text="", font=("Arial", 16), wraplength=400)
        self.question_label.pack(pady=20)

        self.var = tk.IntVar()

        self.radio_buttons = []
        for i in range(3):
            rb = tk.Radiobutton(master, text="", variable=self.var, value=i, font=("Arial", 14))
            rb.pack(anchor="w")
            self.radio_buttons.append(rb)

        self.next_button = tk.Button(master, text="Next", command=self.next_question)
        self.next_button.pack(pady=20)

        self.display_question()

    def display_question(self):
        q, options, _ = self.questions[self.question_number]
        self.question_label.config(text=f"Q{self.question_number + 1}: {q}")
        self.var.set(-1)  # No selection
        for i in range(3):
            self.radio_buttons[i].config(text=options[i])

    def next_question(self):
        selected = self.var.get()
        if selected == -1:
            messagebox.showwarning("Warning", "Please select an answer!")
            return

        correct_index = self.questions[self.question_number][2]
        if selected == correct_index:
            self.score += 1

        self.question_number += 1
        if self.question_number == 10:
            messagebox.showinfo("Quiz Finished", f"You scored {self.score} out of 10!")
            self.master.destroy()
        else:
            self.display_question()

if __name__ == "__main__":
    root = tk.Tk()
    root.geometry("500x350")
    game = QuizGame(root)
    root.mainloop()
