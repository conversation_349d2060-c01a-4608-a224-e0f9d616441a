import pygame
import sys
import random
import os
import time
# Initialize Pygame
pygame.init()

# Screen settings
WIDTH, HEIGHT = 540, 700
WIN = pygame.display.set_mode((WIDTH, HEIGHT))
pygame.display.set_caption("Flappy Bird Pygame")

# Colors
WHITE = (255, 255, 255)
BLACK = (0, 0, 0)
SKY_BLUE = (135, 206, 235)
GREEN = (0, 200, 0)
YELLOW = (255, 255, 0)
RED = (255, 0, 0)

# Fonts
FONT = pygame.font.SysFont("comicsans", 40)
BIG_FONT = pygame.font.SysFont("comicsans", 80)

# Clock
clock = pygame.time.Clock()
FPS = 60

# Bird properties
BIRD_SIZE = 40
GRAVITY = 0.5
FLAP_POWER = -10

# Pipe properties
PIPE_WIDTH = 80
PIPE_GAP = 200
PIPE_SPEED = 4

# Score file
SCORE_FILE = "highscore.txt"


def load_highscore():
    if not os.path.exists(SCORE_FILE):
        with open(SCORE_FILE, 'w') as f:
            f.write("0")
        return 0
    with open(SCORE_FILE, 'r') as f:
        try:
            return int(f.read())
        except:
            return 0


def save_highscore(score):
    highscore = load_highscore()
    if score > highscore:
        with open(SCORE_FILE, 'w') as f:
            f.write(str(score))


class Bird:
    def __init__(self):
        self.x = 100
        self.y = HEIGHT // 2
        self.dy = 0
        self.rect = pygame.Rect(self.x, self.y, BIRD_SIZE, BIRD_SIZE)

    def flap(self):
        self.dy = FLAP_POWER

    def update(self):
        self.dy += GRAVITY
        self.y += self.dy
        self.rect.topleft = (self.x, self.y)

    def draw(self, win):
        pygame.draw.rect(win, YELLOW, self.rect)


class Pipe:
    def __init__(self, x):
        self.x = x
        self.height = random.randint(150, HEIGHT - PIPE_GAP - 150)
        self.top_rect = pygame.Rect(self.x, 0, PIPE_WIDTH, self.height)
        self.bottom_rect = pygame.Rect(self.x, self.height + PIPE_GAP, PIPE_WIDTH, HEIGHT - self.height - PIPE_GAP)
        self.passed = False

    def update(self):
        self.x -= PIPE_SPEED
        self.top_rect.x = self.x
        self.bottom_rect.x = self.x

        if self.x < -PIPE_WIDTH:
            self.x = WIDTH + 100
            self.height = random.randint(150, HEIGHT - PIPE_GAP - 150)
            self.top_rect.height = self.height
            self.bottom_rect.y = self.height + PIPE_GAP
            self.bottom_rect.height = HEIGHT - self.height - PIPE_GAP
            self.passed = False

    def draw(self, win):
        pygame.draw.rect(win, GREEN, self.top_rect)
        pygame.draw.rect(win, GREEN, self.bottom_rect)


def draw_text(win, text, font, color, x, y):
    render = font.render(text, True, color)
    rect = render.get_rect(center=(x, y))
    win.blit(render, rect)


def main_menu():
    while True:
        WIN.fill(SKY_BLUE)
        draw_text(WIN, "Flappy Bird", BIG_FONT, BLACK, WIDTH // 2, HEIGHT // 3)
        draw_text(WIN, "Press SPACE to start", FONT, BLACK, WIDTH // 2, HEIGHT // 2)
        draw_text(WIN, "Press Q to quit", FONT, BLACK, WIDTH // 2, HEIGHT // 2 + 50)
        pygame.display.update()

        for event in pygame.event.get():
            if event.type == pygame.QUIT:
                pygame.quit()
                sys.exit()
            if event.type == pygame.KEYDOWN:
                if event.key == pygame.K_SPACE:
                    
                    game_loop()
                elif event.key == pygame.K_q:
                    pygame.quit()
                    sys.exit()


def game_loop():
    bird = Bird()
    pipes = [Pipe(WIDTH + i * 300) for i in range(3)]
    score = 0
    run = True
    
    while run:
        clock.tick(FPS)
        WIN.fill(SKY_BLUE)

        for event in pygame.event.get():
            if event.type == pygame.QUIT:
                pygame.quit()
                sys.exit()
            if event.type == pygame.KEYDOWN:
                if event.key == pygame.K_SPACE:
                    bird.flap()

        bird.update()
        bird.draw(WIN)

        for pipe in pipes:
            pipe.update()
            pipe.draw(WIN)

            # Collision
            if bird.rect.colliderect(pipe.top_rect) or bird.rect.colliderect(pipe.bottom_rect):
                run = False

            # Score update
            if not pipe.passed and pipe.x + PIPE_WIDTH < bird.x:
                pipe.passed = True
                score += 1

        # Check if bird hits top or bottom
        if bird.y < 0 or bird.y + BIRD_SIZE > HEIGHT:
            run = False

        # Draw score
        draw_text(WIN, f"Score: {score}", FONT, BLACK, WIDTH // 2, 50)

        pygame.display.update()

    save_highscore(score)
    game_over(score)


def game_over(score):
    highscore = load_highscore()
    while True:
        WIN.fill(SKY_BLUE)
        draw_text(WIN, "Game Over", BIG_FONT, RED, WIDTH // 2, HEIGHT // 3)
        draw_text(WIN, f"Score: {score}", FONT, BLACK, WIDTH // 2, HEIGHT // 2)
        draw_text(WIN, f"Highscore: {highscore}", FONT, BLACK, WIDTH // 2, HEIGHT // 2 + 40)
        draw_text(WIN, "Press R to retry or Q to quit", FONT, BLACK, WIDTH // 2, HEIGHT // 2 + 100)
        pygame.display.update()

        for event in pygame.event.get():
            if event.type == pygame.QUIT:
                pygame.quit()
                sys.exit()
            if event.type == pygame.KEYDOWN:
                if event.key == pygame.K_r:
                    game_loop()
                elif event.key == pygame.K_q:
                    pygame.quit()
                    sys.exit()


if __name__ == "__main__":
    main_menu()
